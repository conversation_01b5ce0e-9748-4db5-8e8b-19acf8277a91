// 🌞 SUMMER VACATION CONFIGURATION
// Copy this configuration into the CONFIG object in orar_copii.html

const SUMMER_CONFIG = {
    title: "Summer Fun Schedule",
    subtitle: "Vacation activities for kids",
    language: "en",
    customInstructions: "🌞 Summer vacation time! Remember to drink lots of water, apply sunscreen, and have fun! 🏖️",
    
    timeSlots: [
        "8:00 - 9:00",
        "9:00 - 10:30", 
        "10:30 - 12:00",
        "12:00 - 13:00",
        "13:00 - 14:30",
        "14:30 - 16:00",
        "16:00 - 17:30",
        "17:30 - 19:00",
        "19:00 - 20:00",
        "20:00 - 21:00"
    ],
    
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    
    schedule: {
        "8:00 - 9:00": {
            "Monday": { text: "Wake up & Breakfast", type: "task" },
            "Tuesday": { text: "Wake up & Breakfast", type: "task" },
            "Wednesday": { text: "Wake up & Breakfast", type: "task" },
            "Thursday": { text: "Wake up & Breakfast", type: "task" },
            "Friday": { text: "Wake up & Breakfast", type: "task" },
            "Saturday": { text: "Sleep in & Breakfast", type: "free-time" },
            "Sunday": { text: "Sleep in & Breakfast", type: "free-time" }
        },
        "9:00 - 10:30": {
            "Monday": { text: "Swimming lessons", type: "free-time" },
            "Tuesday": { text: "Art & Crafts", type: "free-time" },
            "Wednesday": { text: "Nature walk", type: "free-time" },
            "Thursday": { text: "Science experiments", type: "homework" },
            "Friday": { text: "Movie time", type: "free-time" },
            "Saturday": { text: "Family outing", type: "free-time" },
            "Sunday": { text: "Reading time", type: "homework" }
        },
        "10:30 - 12:00": {
            "Monday": { text: "Outdoor play", type: "free-time" },
            "Tuesday": { text: "Building/Lego time", type: "free-time" },
            "Wednesday": { text: "Garden activities", type: "chores" },
            "Thursday": { text: "Cooking together", type: "task" },
            "Friday": { text: "Board games", type: "free-time" },
            "Saturday": { text: "Sports/Active play", type: "free-time" },
            "Sunday": { text: "Puzzle time", type: "free-time" }
        },
        "12:00 - 13:00": {
            "Monday": { text: "Lunch", type: "task" },
            "Tuesday": { text: "Lunch", type: "task" },
            "Wednesday": { text: "Lunch", type: "task" },
            "Thursday": { text: "Lunch", type: "task" },
            "Friday": { text: "Lunch", type: "task" },
            "Saturday": { text: "Picnic lunch", type: "task" },
            "Sunday": { text: "Family lunch", type: "task" }
        },
        "13:00 - 14:30": {
            "Monday": { text: "Quiet time/Rest", type: "free-time" },
            "Tuesday": { text: "Quiet time/Rest", type: "free-time" },
            "Wednesday": { text: "Quiet time/Rest", type: "free-time" },
            "Thursday": { text: "Quiet time/Rest", type: "free-time" },
            "Friday": { text: "Quiet time/Rest", type: "free-time" },
            "Saturday": { text: "Free choice activity", type: "free-time" },
            "Sunday": { text: "Family time", type: "free-time" }
        },
        "14:30 - 16:00": {
            "Monday": { text: "Water play", type: "free-time" },
            "Tuesday": { text: "Music & Dance", type: "free-time" },
            "Wednesday": { text: "Bike riding", type: "free-time" },
            "Thursday": { text: "Library visit", type: "homework" },
            "Friday": { text: "Friend playdate", type: "free-time" },
            "Saturday": { text: "Adventure time", type: "free-time" },
            "Sunday": { text: "Creative projects", type: "free-time" }
        },
        "16:00 - 17:30": {
            "Monday": { text: "Help with chores", type: "chores" },
            "Tuesday": { text: "Help with chores", type: "chores" },
            "Wednesday": { text: "Help with chores", type: "chores" },
            "Thursday": { text: "Help with chores", type: "chores" },
            "Friday": { text: "Room cleanup", type: "chores" },
            "Saturday": { text: "Family cleanup", type: "chores" },
            "Sunday": { text: "Prepare for week", type: "chores" }
        },
        "17:30 - 19:00": {
            "Monday": { text: "Free play", type: "free-time" },
            "Tuesday": { text: "Free play", type: "free-time" },
            "Wednesday": { text: "Free play", type: "free-time" },
            "Thursday": { text: "Free play", type: "free-time" },
            "Friday": { text: "Special activity", type: "free-time" },
            "Saturday": { text: "Family activity", type: "free-time" },
            "Sunday": { text: "Relaxation time", type: "free-time" }
        },
        "19:00 - 20:00": {
            "Monday": { text: "Dinner", type: "task" },
            "Tuesday": { text: "Dinner", type: "task" },
            "Wednesday": { text: "Dinner", type: "task" },
            "Thursday": { text: "Dinner", type: "task" },
            "Friday": { text: "Dinner", type: "task" },
            "Saturday": { text: "Dinner", type: "task" },
            "Sunday": { text: "Dinner", type: "task" }
        },
        "20:00 - 21:00": {
            "Monday": { text: "Bath & bedtime story", type: "task" },
            "Tuesday": { text: "Bath & bedtime story", type: "task" },
            "Wednesday": { text: "Bath & bedtime story", type: "task" },
            "Thursday": { text: "Bath & bedtime story", type: "task" },
            "Friday": { text: "Extended playtime", type: "free-time" },
            "Saturday": { text: "Extended playtime", type: "free-time" },
            "Sunday": { text: "Bath & bedtime story", type: "task" }
        }
    },
    
    responsibilities: [
        { text: "Make bed", frequency: "daily", type: "daily" },
        { text: "Put away toys", frequency: "daily", type: "daily" },
        { text: "Help water plants", frequency: "daily", type: "daily" },
        { text: "Feed pets/birds", frequency: "daily", type: "daily" },
        { text: "Help with meals", frequency: "as scheduled", type: "scheduled" },
        { text: "Organize room", frequency: "weekly", type: "weekly" },
        { text: "Help with laundry", frequency: "weekly", type: "weekly" },
        { text: "Summer reading", frequency: "daily", type: "daily" }
    ],
    
    activityTypes: {
        "task": { name: "Daily Tasks", color: "#e8f4f8" },
        "free-time": { name: "Fun Activities", color: "#d9ead3" },
        "homework": { name: "Learning Time", color: "#fff2cc" },
        "chores": { name: "Helping Out", color: "#f4cccc" }
    }
};
