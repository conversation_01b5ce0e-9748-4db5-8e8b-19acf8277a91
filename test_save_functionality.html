<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Save Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn-primary {
            background: #4a86e8;
            color: white;
        }
        .btn-danger {
            background: #ea4335;
            color: white;
        }
        .debug-info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .day-checkbox {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .day-checkbox input {
            width: auto;
            margin-right: 5px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal.active {
            display: flex;
        }
        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }
        .add-activity-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #4a86e8;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(74, 134, 232, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Save Functionality</h1>
        <p>This is a simplified test to check if the save button works.</p>
        
        <button class="add-activity-btn" onclick="openModal()">➕</button>
        
        <div id="debug-info" class="debug-info">Debug information will appear here...</div>
    </div>

    <!-- Modal -->
    <div id="test-modal" class="modal">
        <div class="modal-content">
            <h2>Add New Activity</h2>
            
            <form id="test-form">
                <div class="form-group">
                    <label for="activity-name">Activity Name:</label>
                    <input type="text" id="activity-name" placeholder="Enter activity name" required>
                </div>
                
                <div class="form-group">
                    <label for="start-time">Start Time:</label>
                    <input type="time" id="start-time" required>
                </div>
                
                <div class="form-group">
                    <label for="end-time">End Time:</label>
                    <input type="time" id="end-time" required>
                </div>
                
                <div class="form-group">
                    <label>Select Days:</label>
                    <div class="days-selection">
                        <label class="day-checkbox">
                            <input type="checkbox" class="day-checkbox-input" value="Luni"> Luni
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" class="day-checkbox-input" value="Marți"> Marți
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" class="day-checkbox-input" value="Miercuri"> Miercuri
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>Activity Type:</label>
                    <label><input type="radio" name="activity-type" value="task" checked> Task</label>
                    <label><input type="radio" name="activity-type" value="free-time"> Free Time</label>
                </div>
                
                <button type="button" class="btn btn-danger" onclick="closeModal()">❌ Cancel</button>
                <button type="submit" class="btn btn-primary">💾 Save Activity</button>
            </form>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            document.getElementById('debug-info').textContent = debugLog.join('\n');
            console.log(logEntry);
        }
        
        function openModal() {
            log('Opening modal...');
            document.getElementById('test-modal').classList.add('active');
        }
        
        function closeModal() {
            log('Closing modal...');
            document.getElementById('test-modal').classList.remove('active');
        }
        
        function handleFormSubmit(event) {
            log('Form submit handler called!');
            event.preventDefault();
            
            const activityName = document.getElementById('activity-name').value.trim();
            const startTime = document.getElementById('start-time').value;
            const endTime = document.getElementById('end-time').value;
            const selectedDays = Array.from(document.querySelectorAll('.day-checkbox-input:checked')).map(cb => cb.value);
            const activityType = document.querySelector('input[name="activity-type"]:checked')?.value;
            
            log(`Form data collected:
Activity: ${activityName}
Start: ${startTime}
End: ${endTime}
Days: ${selectedDays.join(', ')}
Type: ${activityType}`);
            
            // Validation
            if (!activityName) {
                log('ERROR: Activity name is required!');
                alert('Please enter activity name!');
                return;
            }
            
            if (!startTime || !endTime) {
                log('ERROR: Start and end time are required!');
                alert('Please select start and end time!');
                return;
            }
            
            if (selectedDays.length === 0) {
                log('ERROR: At least one day must be selected!');
                alert('Please select at least one day!');
                return;
            }
            
            log('SUCCESS: All validation passed!');
            alert(`Activity "${activityName}" saved successfully!`);
            closeModal();
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded - Initializing...');
            
            const form = document.getElementById('test-form');
            if (form) {
                log('Form found, adding submit listener...');
                form.addEventListener('submit', handleFormSubmit);
                log('Submit listener added successfully!');
            } else {
                log('ERROR: Form not found!');
            }
            
            // Also test submit button
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                log('Submit button found!');
                submitBtn.addEventListener('click', function(e) {
                    log('Submit button clicked!');
                });
            } else {
                log('ERROR: Submit button not found!');
            }
            
            log('Initialization complete!');
        });
    </script>
</body>
</html>
