// 👶 TODDLER SCHEDULE CONFIGURATION
// Copy this configuration into the CONFIG object in orar_copii.html

const TODDLER_CONFIG = {
    title: "My Daily Routine",
    subtitle: "Schedule for little ones",
    language: "en",
    customInstructions: "👶 Simple routine for toddlers! Big kids can help check off activities! 🌈",
    
    timeSlots: [
        "7:00 - 8:30",
        "8:30 - 10:00", 
        "10:00 - 11:30",
        "11:30 - 12:30",
        "12:30 - 14:00",
        "14:00 - 15:30",
        "15:30 - 17:00",
        "17:00 - 18:30",
        "18:30 - 19:30",
        "19:30 - 20:30"
    ],
    
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    
    schedule: {
        "7:00 - 8:30": {
            "Monday": { text: "Wake up, Diaper, Breakfast", type: "task" },
            "Tuesday": { text: "Wake up, Diaper, Breakfast", type: "task" },
            "Wednesday": { text: "Wake up, Diaper, Breakfast", type: "task" },
            "Thursday": { text: "Wake up, Diaper, Breakfast", type: "task" },
            "Friday": { text: "Wake up, Diaper, Breakfast", type: "task" },
            "Saturday": { text: "Sleep in, Breakfast", type: "free-time" },
            "Sunday": { text: "Sleep in, Breakfast", type: "free-time" }
        },
        "8:30 - 10:00": {
            "Monday": { text: "Free play time", type: "free-time" },
            "Tuesday": { text: "Sensory play", type: "free-time" },
            "Wednesday": { text: "Music & movement", type: "free-time" },
            "Thursday": { text: "Story time", type: "homework" },
            "Friday": { text: "Art exploration", type: "free-time" },
            "Saturday": { text: "Family playtime", type: "free-time" },
            "Sunday": { text: "Quiet activities", type: "free-time" }
        },
        "10:00 - 11:30": {
            "Monday": { text: "Outdoor time", type: "free-time" },
            "Tuesday": { text: "Building blocks", type: "free-time" },
            "Wednesday": { text: "Water play", type: "free-time" },
            "Thursday": { text: "Shape sorting", type: "homework" },
            "Friday": { text: "Dance party", type: "free-time" },
            "Saturday": { text: "Park visit", type: "free-time" },
            "Sunday": { text: "Puzzle time", type: "homework" }
        },
        "11:30 - 12:30": {
            "Monday": { text: "Lunch time", type: "task" },
            "Tuesday": { text: "Lunch time", type: "task" },
            "Wednesday": { text: "Lunch time", type: "task" },
            "Thursday": { text: "Lunch time", type: "task" },
            "Friday": { text: "Lunch time", type: "task" },
            "Saturday": { text: "Lunch time", type: "task" },
            "Sunday": { text: "Lunch time", type: "task" }
        },
        "12:30 - 14:00": {
            "Monday": { text: "Nap time", type: "task" },
            "Tuesday": { text: "Nap time", type: "task" },
            "Wednesday": { text: "Nap time", type: "task" },
            "Thursday": { text: "Nap time", type: "task" },
            "Friday": { text: "Nap time", type: "task" },
            "Saturday": { text: "Quiet rest", type: "free-time" },
            "Sunday": { text: "Quiet rest", type: "free-time" }
        },
        "14:00 - 15:30": {
            "Monday": { text: "Snack & play", type: "free-time" },
            "Tuesday": { text: "Snack & books", type: "homework" },
            "Wednesday": { text: "Snack & toys", type: "free-time" },
            "Thursday": { text: "Snack & colors", type: "homework" },
            "Friday": { text: "Snack & music", type: "free-time" },
            "Saturday": { text: "Snack & family time", type: "free-time" },
            "Sunday": { text: "Snack & cuddles", type: "free-time" }
        },
        "15:30 - 17:00": {
            "Monday": { text: "Active play", type: "free-time" },
            "Tuesday": { text: "Pretend play", type: "free-time" },
            "Wednesday": { text: "Nature exploration", type: "free-time" },
            "Thursday": { text: "Learning games", type: "homework" },
            "Friday": { text: "Special activity", type: "free-time" },
            "Saturday": { text: "Adventure time", type: "free-time" },
            "Sunday": { text: "Calm activities", type: "free-time" }
        },
        "17:00 - 18:30": {
            "Monday": { text: "Help with simple tasks", type: "chores" },
            "Tuesday": { text: "Put toys away", type: "chores" },
            "Wednesday": { text: "Help feed pets", type: "chores" },
            "Thursday": { text: "Sort shapes/colors", type: "chores" },
            "Friday": { text: "Help set table", type: "chores" },
            "Saturday": { text: "Family cleanup", type: "chores" },
            "Sunday": { text: "Organize toys", type: "chores" }
        },
        "18:30 - 19:30": {
            "Monday": { text: "Dinner time", type: "task" },
            "Tuesday": { text: "Dinner time", type: "task" },
            "Wednesday": { text: "Dinner time", type: "task" },
            "Thursday": { text: "Dinner time", type: "task" },
            "Friday": { text: "Dinner time", type: "task" },
            "Saturday": { text: "Dinner time", type: "task" },
            "Sunday": { text: "Dinner time", type: "task" }
        },
        "19:30 - 20:30": {
            "Monday": { text: "Bath, story, bedtime", type: "task" },
            "Tuesday": { text: "Bath, story, bedtime", type: "task" },
            "Wednesday": { text: "Bath, story, bedtime", type: "task" },
            "Thursday": { text: "Bath, story, bedtime", type: "task" },
            "Friday": { text: "Bath, extra story time", type: "task" },
            "Saturday": { text: "Bath, extra story time", type: "task" },
            "Sunday": { text: "Bath, story, bedtime", type: "task" }
        }
    },
    
    responsibilities: [
        { text: "Put toys in basket", frequency: "daily", type: "daily" },
        { text: "Help brush teeth", frequency: "twice daily", type: "daily" },
        { text: "Wash hands", frequency: "before meals", type: "daily" },
        { text: "Help feed pets", frequency: "daily", type: "daily" },
        { text: "Put shoes away", frequency: "daily", type: "daily" },
        { text: "Help with laundry (sorting)", frequency: "weekly", type: "weekly" },
        { text: "Water plants (with help)", frequency: "weekly", type: "weekly" }
    ],
    
    activityTypes: {
        "task": { name: "Daily Care", color: "#e8f4f8" },
        "free-time": { name: "Play Time", color: "#d9ead3" },
        "homework": { name: "Learning", color: "#fff2cc" },
        "chores": { name: "Helping", color: "#f4cccc" }
    }
};
