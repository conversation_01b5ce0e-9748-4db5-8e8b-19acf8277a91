<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Save Button</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn-primary {
            background: #4a86e8;
            color: white;
        }
        .btn-danger {
            background: #ea4335;
            color: white;
        }
        .debug-info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
        }
        .day-checkbox {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 10px;
        }
        .day-checkbox input {
            width: auto;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Save Button Test</h1>
        
        <form id="test-form">
            <div class="form-group">
                <label for="activity-name">Activity Name:</label>
                <input type="text" id="activity-name" placeholder="Enter activity name" required>
            </div>
            
            <div class="form-group">
                <label for="start-time">Start Time:</label>
                <input type="time" id="start-time" required>
            </div>
            
            <div class="form-group">
                <label for="end-time">End Time:</label>
                <input type="time" id="end-time" required>
            </div>
            
            <div class="form-group">
                <label>Select Days:</label>
                <div class="days-selection">
                    <label class="day-checkbox">
                        <input type="checkbox" value="Luni"> Luni
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="Marți"> Marți
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="Miercuri"> Miercuri
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="Joi"> Joi
                    </label>
                    <label class="day-checkbox">
                        <input type="checkbox" value="Vineri"> Vineri
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label>Activity Type:</label>
                <label><input type="radio" name="activity-type" value="task" checked> Task</label>
                <label><input type="radio" name="activity-type" value="free-time"> Free Time</label>
                <label><input type="radio" name="activity-type" value="homework"> Homework</label>
                <label><input type="radio" name="activity-type" value="chores"> Chores</label>
            </div>
            
            <button type="button" class="btn btn-danger" onclick="resetForm()">❌ Reset</button>
            <button type="submit" class="btn btn-primary">💾 Save Activity</button>
        </form>
        
        <div id="debug-info" class="debug-info">
            <strong>Debug Information:</strong><br>
            <div id="debug-output">Form not submitted yet...</div>
        </div>
    </div>

    <script>
        // Debug function to log all form data
        function debugFormData() {
            const activityName = document.getElementById('activity-name').value.trim();
            const startTime = document.getElementById('start-time').value;
            const endTime = document.getElementById('end-time').value;
            const selectedDays = Array.from(document.querySelectorAll('.day-checkbox input:checked')).map(cb => cb.value);
            const activityType = document.querySelector('input[name="activity-type"]:checked')?.value;
            
            return {
                activityName,
                startTime,
                endTime,
                selectedDays,
                activityType,
                timestamp: new Date().toLocaleString()
            };
        }
        
        // Handle form submission
        function handleFormSubmit(event) {
            event.preventDefault();
            console.log('Form submit event triggered!');
            
            const formData = debugFormData();
            console.log('Form data:', formData);
            
            // Update debug display
            const debugOutput = document.getElementById('debug-output');
            debugOutput.innerHTML = `
                <strong>Form Submitted Successfully!</strong><br>
                Activity Name: ${formData.activityName}<br>
                Start Time: ${formData.startTime}<br>
                End Time: ${formData.endTime}<br>
                Selected Days: ${formData.selectedDays.join(', ')}<br>
                Activity Type: ${formData.activityType}<br>
                Timestamp: ${formData.timestamp}
            `;
            
            // Validation
            if (!formData.activityName) {
                alert('Please enter activity name!');
                return;
            }
            
            if (!formData.startTime || !formData.endTime) {
                alert('Please select start and end time!');
                return;
            }
            
            if (formData.selectedDays.length === 0) {
                alert('Please select at least one day!');
                return;
            }
            
            alert(`Activity "${formData.activityName}" saved successfully!`);
        }
        
        // Reset form
        function resetForm() {
            document.getElementById('test-form').reset();
            document.getElementById('debug-output').innerHTML = 'Form reset...';
        }
        
        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing event listeners...');
            
            const form = document.getElementById('test-form');
            if (form) {
                form.addEventListener('submit', handleFormSubmit);
                console.log('Form submit listener added successfully!');
            } else {
                console.error('Form not found!');
            }
            
            // Test button click
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.addEventListener('click', function(e) {
                    console.log('Submit button clicked!');
                });
            }
        });
    </script>
</body>
</html>
