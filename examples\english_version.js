// 🇺🇸 ENGLISH VERSION CONFIGURATION
// Copy this configuration into the CONFIG object in orar_copii.html

const ENGLISH_CONFIG = {
    title: "Children's Daily Schedule",
    subtitle: "Daily routine for kids",
    language: "en",
    customInstructions: "Remember to check off each activity when you complete it! 🌟",
    
    timeSlots: [
        "7:00 - 8:00",
        "8:00 - 9:00", 
        "9:00 - 10:00",
        "10:00 - 11:00",
        "11:00 - 12:00",
        "12:00 - 13:00",
        "13:00 - 14:00",
        "14:00 - 15:00",
        "15:00 - 16:00",
        "16:00 - 17:00",
        "17:00 - 18:00",
        "18:00 - 19:00",
        "19:00 - 20:00",
        "20:00 - 21:00"
    ],
    
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
    
    schedule: {
        "7:00 - 8:00": {
            "Monday": { text: "Wake up, Breakfast", type: "task" },
            "Tuesday": { text: "Wake up, Breakfast", type: "task" },
            "Wednesday": { text: "Wake up, Breakfast", type: "task" },
            "Thursday": { text: "Wake up, Breakfast", type: "task" },
            "Friday": { text: "Wake up, Breakfast", type: "task" },
            "Saturday": { text: "Sleep in", type: "free-time" },
            "Sunday": { text: "Sleep in", type: "free-time" }
        },
        "8:00 - 9:00": {
            "Monday": { text: "Get ready for school", type: "task" },
            "Tuesday": { text: "Get ready for school", type: "task" },
            "Wednesday": { text: "Get ready for school", type: "task" },
            "Thursday": { text: "Get ready for school", type: "task" },
            "Friday": { text: "Get ready for school", type: "task" },
            "Saturday": { text: "Breakfast", type: "task" },
            "Sunday": { text: "Breakfast", type: "task" }
        },
        "9:00 - 10:00": {
            "Monday": { text: "School", type: "homework" },
            "Tuesday": { text: "School", type: "homework" },
            "Wednesday": { text: "School", type: "homework" },
            "Thursday": { text: "School", type: "homework" },
            "Friday": { text: "School", type: "homework" },
            "Saturday": { text: "Free play", type: "free-time" },
            "Sunday": { text: "Family time", type: "free-time" }
        },
        "12:00 - 13:00": {
            "Monday": { text: "Lunch", type: "task" },
            "Tuesday": { text: "Lunch", type: "task" },
            "Wednesday": { text: "Lunch", type: "task" },
            "Thursday": { text: "Lunch", type: "task" },
            "Friday": { text: "Lunch", type: "task" },
            "Saturday": { text: "Lunch", type: "task" },
            "Sunday": { text: "Lunch", type: "task" }
        },
        "15:00 - 16:00": {
            "Monday": { text: "Homework", type: "homework" },
            "Tuesday": { text: "Homework", type: "homework" },
            "Wednesday": { text: "Homework", type: "homework" },
            "Thursday": { text: "Homework", type: "homework" },
            "Friday": { text: "Free time", type: "free-time" },
            "Saturday": { text: "Activities", type: "free-time" },
            "Sunday": { text: "Prepare for week", type: "task" }
        },
        "17:00 - 18:00": {
            "Monday": { text: "Chores", type: "chores" },
            "Tuesday": { text: "Chores", type: "chores" },
            "Wednesday": { text: "Chores", type: "chores" },
            "Thursday": { text: "Chores", type: "chores" },
            "Friday": { text: "Chores", type: "chores" },
            "Saturday": { text: "Family chores", type: "chores" },
            "Sunday": { text: "Room cleanup", type: "chores" }
        },
        "19:00 - 20:00": {
            "Monday": { text: "Dinner", type: "task" },
            "Tuesday": { text: "Dinner", type: "task" },
            "Wednesday": { text: "Dinner", type: "task" },
            "Thursday": { text: "Dinner", type: "task" },
            "Friday": { text: "Dinner", type: "task" },
            "Saturday": { text: "Dinner", type: "task" },
            "Sunday": { text: "Dinner", type: "task" }
        },
        "20:00 - 21:00": {
            "Monday": { text: "Bedtime routine", type: "task" },
            "Tuesday": { text: "Bedtime routine", type: "task" },
            "Wednesday": { text: "Bedtime routine", type: "task" },
            "Thursday": { text: "Bedtime routine", type: "task" },
            "Friday": { text: "Free time", type: "free-time" },
            "Saturday": { text: "Free time", type: "free-time" },
            "Sunday": { text: "Bedtime routine", type: "task" }
        }
    },
    
    responsibilities: [
        { text: "Make bed", frequency: "daily", type: "daily" },
        { text: "Brush teeth", frequency: "twice daily", type: "daily" },
        { text: "Put away toys", frequency: "daily", type: "daily" },
        { text: "Feed pets", frequency: "daily", type: "daily" },
        { text: "Help set table", frequency: "as scheduled", type: "scheduled" },
        { text: "Sort dirty clothes", frequency: "weekly", type: "weekly" },
        { text: "Clean room", frequency: "weekly", type: "weekly" },
        { text: "Help with groceries", frequency: "as needed", type: "asneeded" }
    ],
    
    activityTypes: {
        "task": { name: "Daily Tasks", color: "#e8f4f8" },
        "free-time": { name: "Free Time", color: "#d9ead3" },
        "homework": { name: "School/Study", color: "#fff2cc" },
        "chores": { name: "Household Chores", color: "#f4cccc" }
    }
};
